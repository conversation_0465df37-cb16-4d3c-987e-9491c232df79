# Codebase Overview

This repository contains a small **Next.js** project written in TypeScript. It uses the new **app directory** structure and Tailwind CSS for styling. The project is a front-end demo with hard-coded data (no backend).

## Structure
- `app/` – Next.js pages and React components
- `public/` – static assets
- `tailwind.config.js` – Tailwind CSS configuration
- `biome.json` – lint/format rules for Biome
- `next.config.js` – Next.js options
- `package.json` – project scripts and dependencies
- `pnpm-lock.yaml` – lockfile (use `pnpm`)

`app/layout.tsx` applies global styles and wraps pages with `Header` and `Footer`. `app/page.tsx` renders the home page components (Hero, FeaturedBots, PerformanceStats, etc.). The blog lives under `app/blog/` with a dynamic `[slug]` route. Global styles and animations are defined in `app/globals.css`.

## Development with pnpm
Run all Node commands with **pnpm**:

- `pnpm install` – install dependencies
- `pnpm dev` – start the development server
- `pnpm build` – create a production build
- `pnpm start` – run the built app
- `pnpm lint` – check code with Biome
- `pnpm format` – format files using Biome

There are currently no tests.
