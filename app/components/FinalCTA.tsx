import { ArrowR<PERSON>, <PERSON>rk<PERSON> } from "lucide-react";
import React from "react";

const FinalCTA = () => {
  return (
    <section className="py-20 bg-black relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-grid-pattern opacity-10" />
      <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-blue-600/20 to-transparent rounded-full blur-3xl animate-float" />
      <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-gradient-to-r from-amber-500/20 to-transparent rounded-full blur-3xl animate-float delay-1000" />

      <div className="relative z-10 max-w-4xl mx-auto px-6 text-center">
        <div className="mb-8">
          <Sparkles className="w-16 h-16 text-blue-400 mx-auto mb-6 animate-pulse" />
          <h2 className="text-4xl md:text-6xl font-bold text-white mb-6 animate-fade-in-up">
            Ready to Automate Your Trading?
          </h2>
          <p className="text-xl text-gray-300 mb-4 leading-relaxed animate-fade-in-up delay-200">
            Get started today with a 7-day free trial.
          </p>
          <p className="text-lg text-gray-400 mb-12 animate-fade-in-up delay-300">
            No setup fees. No code. 100% results-driven.
          </p>
        </div>
        <div className="flex flex-col sm:flex-row gap-4 justify-center animate-fade-in-up delay-400">
          <button
            className="group bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-500 hover:to-blue-400 text-white px-10 py-5 rounded-xl font-bold text-xl transition-all duration-300 transform hover:scale-105 glow-blue"
            type="button"
          >
            <span className="flex items-center gap-3">
              Create Free Account
              <ArrowRight className="w-6 h-6 group-hover:translate-x-1 transition-transform" />
            </span>
          </button>

          <button
            className="border-2 border-gray-700 text-gray-300 hover:bg-gray-800 hover:text-white hover:border-gray-600 px-10 py-5 rounded-xl font-bold text-xl transition-all duration-300 transform hover:scale-105"
            type="button"
          >
            Explore Bots
          </button>
        </div>
        {/* Trust Indicators */}
        <div className="mt-16 flex flex-wrap justify-center items-center gap-8 text-gray-500 text-sm">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse" />
            <span>SSL Encrypted</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-amber-400 rounded-full animate-pulse delay-500" />
            <span>7-Day Free Trial</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse delay-1000" />
            <span>No Credit Card Required</span>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FinalCTA;
