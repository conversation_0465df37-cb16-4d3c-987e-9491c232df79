import { Quote, <PERSON> } from "lucide-react";

const Testimonials = () => {
  const testimonials = [
    {
      quote: "I made 18% in just one month using AlphaX bot — without lifting a finger.",
      author: "<PERSON>",
      location: "Singapore",
      avatar:
        "https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop&crop=face",
    },
    {
      quote: "Finally found a platform that blends AI and trading without the fluff.",
      author: "Aria K.",
      location: "UK",
      avatar:
        "https://images.pexels.com/photos/415829/pexels-photo-415829.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop&crop=face",
    },
    {
      quote: "The transparency and real ROI metrics give me total confidence.",
      author: "Minh T.",
      location: "Vietnam",
      avatar:
        "https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop&crop=face",
    },
  ];

  return (
    <section className="py-20 bg-gray-900">
      <div className="max-w-7xl mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-4 animate-fade-in-up">
            💬 What Our Users Say
          </h2>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <div
              // biome-ignore lint/suspicious/noArrayIndexKey: testimonials array order is constant
              key={index}
              className="group bg-black border border-gray-800 rounded-3xl p-8 hover:bg-gray-800/50 hover:border-gray-700 transition-all duration-500 animate-scale-in"
              style={{ animationDelay: `${index * 150}ms` }}
            >
              <Quote className="w-8 h-8 text-blue-400 mb-4 opacity-50 group-hover:scale-110 transition-transform duration-300" />

              <p className="text-gray-300 leading-relaxed mb-6 text-lg italic">
                "{testimonial.quote}"
              </p>

              <div className="flex items-center gap-4">
                <img
                  src={testimonial.avatar}
                  alt={testimonial.author}
                  className="w-12 h-12 rounded-full object-cover border-2 border-gray-700"
                />
                <div>
                  <p className="font-semibold text-white">— {testimonial.author}</p>
                  <p className="text-gray-400 text-sm">{testimonial.location}</p>
                </div>
              </div>

              <div className="flex gap-1 mt-4">
                {[1, 2, 3, 4, 5].map((star) => (
                  <Star
                    key={star}
                    className="w-5 h-5 text-amber-400 fill-current group-hover:animate-pulse"
                    style={{ animationDelay: `${star * 100}ms` }}
                  />
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* Social Proof Badges */}
        <div className="flex justify-center items-center gap-8 mt-16 opacity-60">
          <div className="text-gray-400 font-medium">Trusted by 5,200+ traders</div>
          <div className="text-gray-400 font-medium">Bank-level security</div>
          <div className="text-gray-400 font-medium">99.9% uptime</div>
        </div>
      </div>
    </section>
  );
};

export default Testimonials;
