{"$schema": "https://biomejs.dev/schemas/2.0.5/schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "files": {"ignoreUnknown": false}, "formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 2, "lineWidth": 100}, "linter": {"enabled": true, "rules": {"recommended": true, "style": {"noParameterAssign": "error", "useAsConstAssertion": "error", "useDefaultParameterLast": "error", "useEnumInitializers": "error", "useSelfClosingElements": "error", "useSingleVarDeclarator": "error", "noUnusedTemplateLiteral": "error", "useNumberNamespace": "error", "noInferrableTypes": "error", "noUselessElse": "error"}}}, "javascript": {"formatter": {"quoteStyle": "double", "semicolons": "always", "trailingCommas": "es5"}}, "json": {"formatter": {"enabled": true}}, "css": {"formatter": {"enabled": true}}, "assist": {"enabled": true, "actions": {"source": {"organizeImports": "on"}}}}